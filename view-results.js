#!/usr/bin/env node

const XLSX = require('xlsx');

function viewResults() {
    try {
        // 读取 Excel 文件
        const workbook = XLSX.readFile('dependency-batches.xlsx');
        
        // 读取主工作表
        const mainSheet = workbook.Sheets['Dependency Batches'];
        const mainData = XLSX.utils.sheet_to_json(mainSheet, { header: 1 });
        
        console.log('=== NPM 包依赖分析结果 ===\n');
        
        // 统计每个批次的包数量
        const batchStats = {};
        const packages = [];
        
        // 跳过标题行
        for (let i = 1; i < mainData.length; i++) {
            const [packageName, batchNumber] = mainData[i];
            if (packageName && batchNumber) {
                packages.push({ name: packageName, batch: batchNumber });
                if (!batchStats[batchNumber]) {
                    batchStats[batchNumber] = 0;
                }
                batchStats[batchNumber]++;
            }
        }
        
        console.log(`总包数: ${packages.length}`);
        console.log(`总批次数: ${Math.max(...Object.keys(batchStats).map(Number))}`);
        console.log();
        
        // 显示每个批次的统计信息
        console.log('批次统计:');
        Object.keys(batchStats)
            .sort((a, b) => parseInt(a) - parseInt(b))
            .forEach(batch => {
                console.log(`  批次 ${batch}: ${batchStats[batch]} 个包`);
            });
        
        console.log();
        
        // 显示每个批次的详细包列表
        console.log('详细批次信息:');
        Object.keys(batchStats)
            .sort((a, b) => parseInt(a) - parseInt(b))
            .forEach(batch => {
                console.log(`\n批次 ${batch} (${batchStats[batch]} 个包):`);
                const batchPackages = packages
                    .filter(pkg => pkg.batch == batch)
                    .map(pkg => pkg.name)
                    .sort();
                
                batchPackages.forEach(pkg => {
                    console.log(`  - ${pkg}`);
                });
            });
        
        // 读取依赖详情工作表
        if (workbook.Sheets['Dependency Details']) {
            const detailSheet = workbook.Sheets['Dependency Details'];
            const detailData = XLSX.utils.sheet_to_json(detailSheet, { header: 1 });
            
            console.log('\n=== 依赖关系统计 ===');
            
            let totalDeps = 0;
            let packagesWithDeps = 0;
            
            for (let i = 1; i < detailData.length; i++) {
                const [packageName, dependencies, depCount] = detailData[i];
                if (depCount > 0) {
                    packagesWithDeps++;
                    totalDeps += depCount;
                }
            }
            
            console.log(`有依赖关系的包: ${packagesWithDeps}/${packages.length}`);
            console.log(`总依赖关系数: ${totalDeps}`);
            console.log(`平均每个包的依赖数: ${(totalDeps / packages.length).toFixed(2)}`);
        }
        
        // 检查是否有错误信息
        if (workbook.Sheets['Errors']) {
            const errorSheet = workbook.Sheets['Errors'];
            const errorData = XLSX.utils.sheet_to_json(errorSheet, { header: 1 });
            
            console.log('\n=== 错误信息 ===');
            for (let i = 1; i < errorData.length; i++) {
                if (errorData[i][0]) {
                    console.log(`  - ${errorData[i][0]}`);
                }
            }
        }
        
        console.log('\n=== 修改建议 ===');
        console.log('根据依赖关系分析，建议按以下顺序进行包的修改:');
        console.log('1. 先修改批次 1 的包（无依赖的基础包）');
        console.log('2. 然后修改批次 2 的包（只依赖批次 1 的包）');
        console.log('3. 依此类推，确保依赖包总是在被依赖包之前修改');
        console.log('\n详细信息请查看生成的 Excel 文件: dependency-batches.xlsx');
        
    } catch (error) {
        console.error('读取结果文件失败:', error.message);
        console.log('请确保 dependency-batches.xlsx 文件存在');
    }
}

viewResults();
