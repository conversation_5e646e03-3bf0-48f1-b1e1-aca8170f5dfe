#!/usr/bin/env node

const XLSX = require('xlsx');

function readListXlsx() {
    try {
        // 读取 Excel 文件
        const workbook = XLSX.readFile('list.xlsx');
        
        console.log('工作表列表:', workbook.SheetNames);
        
        // 读取每个工作表
        workbook.SheetNames.forEach(sheetName => {
            console.log(`\n=== 工作表: ${sheetName} ===`);
            const worksheet = workbook.Sheets[sheetName];
            const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
            
            console.log('前20行数据:');
            data.slice(0, 20).forEach((row, index) => {
                console.log(`第${index + 1}行:`, row);
            });

            if (data.length > 20) {
                console.log(`... 还有 ${data.length - 20} 行数据`);
            }

            console.log(`\n总行数: ${data.length}`);

            // 查找有依赖的包
            console.log('\n有依赖的包:');
            data.slice(1).forEach((row, index) => {
                if (row[1] && row[1].trim()) {
                    console.log(`${row[0]}: ${row[1]}`);
                }
            });
        });
        
    } catch (error) {
        console.error('读取 list.xlsx 失败:', error.message);
    }
}

readListXlsx();
