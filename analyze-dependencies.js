#!/usr/bin/env node

const fs = require('fs');
const { execSync } = require('child_process');
const path = require('path');
const XLSX = require('xlsx');

// Mock 数据 - 模拟一些包的依赖关系
const MOCK_DEPENDENCIES = {
    '@mu/madp-base': [],
    '@mu/madp-utils': ['@mu/madp-base'],
    '@mu/madp': ['@mu/madp-base', '@mu/madp-utils'],
    '@mu/madp-security': ['@mu/madp-base'],
    '@mu/madp-track': ['@mu/madp-base'],
    '@mu/madp-h2o': ['@mu/madp', '@mu/madp-utils'],
    '@mu/cui': ['@mu/madp-base'],
    '@mu/lui': ['@mu/madp-base'],
    '@mu/tui': ['@mu/madp-base'],
    '@mu/zui': ['@mu/madp-base'],
    '@mu/op': ['@mu/madp'],
    '@mu/op-ui': ['@mu/op', '@mu/cui'],
    '@mu/op-comp': ['@mu/op', '@mu/cui'],
    '@mu/op-comp-homepage': ['@mu/op-comp'],
    '@mu/op-comp-kwai': ['@mu/op-comp'],
    '@mu/basic-library': [],
    '@mu/business-basic': ['@mu/basic-library'],
    '@mu/business-plugin': ['@mu/business-basic'],
    '@mu/business-plugin-utils': ['@mu/business-plugin'],
    '@mu/login-utils': ['@mu/basic-library'],
    '@mu/login-popup': ['@mu/login-utils', '@mu/cui'],
    '@mu/apply-login-comp': ['@mu/login-popup'],
    '@mu/agreement': ['@mu/cui'],
    '@mu/agreement-new': ['@mu/agreement'],
    '@mu/agreement-popup': ['@mu/agreement'],
    '@mu/ubf-agreement-view': ['@mu/agreement'],
    '@mu/bio-auth': ['@mu/madp-base'],
    '@mu/bio-auth-ali': ['@mu/bio-auth'],
    '@mu/bio-auth-zfb': ['@mu/bio-auth'],
    '@mu/biometrics': ['@mu/bio-auth'],
    '@mu/biometrics-shell': ['@mu/biometrics'],
    '@mu/biometrics-utils': ['@mu/biometrics'],
    '@mu/supply-bio': ['@mu/biometrics'],
    '@mu/tarosdk-mu-bio-auth': ['@mu/bio-auth'],
    '@mu/homepage': ['@mu/madp', '@mu/cui'],
    '@mu/homepage-a2': ['@mu/homepage'],
    '@mu/homepage-acq': ['@mu/homepage'],
    '@mu/mypage': ['@mu/madp', '@mu/cui'],
    '@mu/mypage-a2': ['@mu/mypage'],
    '@mu/mypage-acq': ['@mu/mypage'],
    '@mu/mypage-template': ['@mu/mypage'],
    '@mu/credit-card': ['@mu/madp', '@mu/cui'],
    '@mu/credit-card-new': ['@mu/credit-card'],
    '@mu/credit-basic-card': ['@mu/credit-card'],
    '@mu/credit-basic-card-utils': ['@mu/credit-basic-card'],
    '@mu/cuf-credit-card': ['@mu/credit-card'],
    '@mu/fe-logger': ['@mu/madp-base'],
    '@mu/datacollect': ['@mu/fe-logger'],
    '@mu/das-beacon': ['@mu/datacollect'],
    '@mu/user-bhvr-detector': ['@mu/datacollect'],
    '@mu/geetest-shell': ['@mu/madp-base'],
    '@mu/geestest': ['@mu/geetest-shell'],
    '@mu/safe-sms-shell': ['@mu/madp-base'],
    '@mu/safe-sms-code': ['@mu/safe-sms-shell'],
    '@mu/trade-password-encrypted-shell': ['@mu/madp-base'],
    '@mu/trade-verify-shell': ['@mu/madp-base'],
    '@mu/trade-verify': ['@mu/trade-verify-shell'],
    '@mu/onepass-shell': ['@mu/madp-base'],
    '@mu/validate-auth': ['@mu/onepass-shell'],
    '@mu/render-taro': ['@mu/madp-base'],
    '@mu/taro-adv': ['@mu/render-taro'],
    '@mu/taro-df17-component': ['@mu/render-taro'],
    '@mu/eight-grid': ['@mu/cui'],
    '@mu/tabbar': ['@mu/cui'],
    '@mu/swiper-custom': ['@mu/cui'],
    '@mu/text-progress-bar': ['@mu/cui'],
    '@mu/result-loading': ['@mu/cui'],
    '@mu/sy-dialog': ['@mu/cui'],
    '@mu/retain-dialog': ['@mu/sy-dialog'],
    '@mu/covenant-dialog': ['@mu/sy-dialog'],
    '@mu/subscribe-btn': ['@mu/cui'],
    '@mu/click-selector': ['@mu/cui'],
    '@mu/range-date-select-drawer': ['@mu/cui'],
    '@mu/coupon-selector': ['@mu/cui'],
    '@mu/coupon-selector-drawer': ['@mu/coupon-selector'],
    '@mu/coupon-selector-utils': ['@mu/coupon-selector'],
    '@mu/vague-search': ['@mu/cui'],
    '@mu/photo-upload': ['@mu/cui'],
    '@mu/selfie': ['@mu/photo-upload'],
    '@mu/id-card-ocr': ['@mu/photo-upload'],
    '@mu/idcard-ocr': ['@mu/id-card-ocr'],
    '@mu/bankcard': ['@mu/photo-upload'],
    '@mu/vehicle-certify': ['@mu/photo-upload'],
    '@mu/video-chat': ['@mu/cui'],
    '@mu/video-verify': ['@mu/video-chat'],
    '@mu/survey': ['@mu/cui'],
    '@mu/todo': ['@mu/cui'],
    '@mu/unhandle-notice': ['@mu/cui'],
    '@mu/vue-banner': ['@mu/cui'],
    '@mu/wa-richtext': ['@mu/cui'],
    '@mu/wgk-component': ['@mu/cui'],
    '@mu/mini-html-parser2': [],
    '@mu/local-config': [],
    '@mu/fetch-upaas-api': ['@mu/madp-base'],
    '@mu/call-muapp': ['@mu/madp-base'],
    '@mu/safe-get-token': ['@mu/madp-base'],
    '@mu/safety-get-token': ['@mu/safe-get-token'],
    '@mu/dev-finger': ['@mu/madp-base'],
    '@mu/input-for-cx-svc': ['@mu/cui'],
    '@mu/ldf-flow-card': ['@mu/cui'],
    '@mu/lds-comp': ['@mu/cui'],
    '@mu/lds-standard-entry': ['@mu/lds-comp'],
    '@mu/leda': ['@mu/cui'],
    '@mu/loan-help': ['@mu/cui'],
    '@mu/short-loan-card': ['@mu/cui'],
    '@mu/silence-unfreeze-comp': ['@mu/cui'],
    '@mu/benefit-card': ['@mu/cui'],
    '@mu/bcp-component': ['@mu/cui'],
    '@mu/bl': ['@mu/cui'],
    '@mu/apm': ['@mu/madp-base'],
    '@mu/abf-apply-utils': ['@mu/cui'],
    '@mu/apply-case-guide-dialog': ['@mu/cui'],
    '@mu/bio-app': ['@mu/biometrics'],
    '@mu/channel-business': ['@mu/business-basic'],
    '@mu/channel-financial-services': ['@mu/channel-business'],
    '@mu/channel-intention': ['@mu/channel-business'],
    '@mu/channel-repay-services': ['@mu/channel-business'],
    '@mu/channel-restore-credit': ['@mu/channel-business'],
    '@mu/channel-template': ['@mu/channel-business'],
    '@mu/chat-entry-component': ['@mu/cui'],
    '@mu/cmb-unite-apply': ['@mu/cui'],
    '@mu/csp-client-extend-sdk': ['@mu/madp-base'],
    '@mu/cuf-float-image': ['@mu/cui'],
    '@mu/cuf-supply-sign-modal': ['@mu/cui'],
    '@mu/cuf-unicomhome-fe': ['@mu/cui'],
    '@mu/hospital-group': ['@mu/cui'],
    '@mu/madp-cli': ['@mu/madp-base'],
    '@mu/marketing-lake': ['@mu/cui'],
    '@mu/order-group': ['@mu/cui'],
    '@mu/overdue-repay-services': ['@mu/cui'],
    '@mu/restore-credit-comp': ['@mu/cui'],
    '@mu/school-group': ['@mu/cui'],
    '@mu/zllegao': ['@mu/cui'],
    '@mu/babel-plugin-transform-madpapi': []
};

// 读取包列表
function readPackageList() {
    try {
        const content = fs.readFileSync('list.md', 'utf8');
        return content.split('\n')
            .map(line => line.trim())
            .filter(line => line && line.startsWith('@mu/'));
    } catch (error) {
        console.error('Error reading list.md:', error.message);
        process.exit(1);
    }
}

// 获取包的依赖信息 (使用 Mock 数据)
async function getPackageDependencies(packageName) {
    // 使用 Mock 数据
    const mockDeps = MOCK_DEPENDENCIES[packageName];
    if (mockDeps !== undefined) {
        return mockDeps;
    }

    // 如果 Mock 数据中没有该包，返回空数组
    console.warn(`Warning: No mock data for package ${packageName}, assuming no dependencies`);
    return [];
}

// 拓扑排序算法
function topologicalSort(packages, dependencies) {
    const graph = new Map();
    const inDegree = new Map();
    const result = [];
    const batches = [];
    
    // 初始化图和入度
    packages.forEach(pkg => {
        graph.set(pkg, []);
        inDegree.set(pkg, 0);
    });
    
    // 构建图
    packages.forEach(pkg => {
        const deps = dependencies.get(pkg) || [];
        deps.forEach(dep => {
            if (packages.includes(dep)) {
                graph.get(dep).push(pkg);
                inDegree.set(pkg, inDegree.get(pkg) + 1);
            }
        });
    });
    
    // 拓扑排序
    let batchNumber = 1;
    
    while (result.length < packages.length) {
        // 找到所有入度为0的节点
        const currentBatch = [];
        packages.forEach(pkg => {
            if (!result.includes(pkg) && inDegree.get(pkg) === 0) {
                currentBatch.push(pkg);
            }
        });
        
        if (currentBatch.length === 0) {
            // 检测到循环依赖
            const remaining = packages.filter(pkg => !result.includes(pkg));
            console.error('Circular dependency detected among packages:', remaining);
            
            // 将剩余包分配到当前批次
            remaining.forEach(pkg => {
                batches.push({ package: pkg, batch: batchNumber });
                result.push(pkg);
            });
            break;
        }
        
        // 将当前批次的包添加到结果中
        currentBatch.sort(); // 按字母顺序排序
        currentBatch.forEach(pkg => {
            batches.push({ package: pkg, batch: batchNumber });
            result.push(pkg);
            
            // 更新依赖包的入度
            graph.get(pkg).forEach(dependent => {
                inDegree.set(dependent, inDegree.get(dependent) - 1);
            });
        });
        
        batchNumber++;
    }
    
    return batches;
}

// 生成Excel文件
function generateExcel(batches, errors, dependencies) {
    // 创建工作簿
    const workbook = XLSX.utils.book_new();

    // 准备主要数据
    const mainData = [['Package Name', 'Batch Number']];
    batches.forEach(item => {
        mainData.push([item.package, item.batch]);
    });

    // 创建主工作表
    const mainWorksheet = XLSX.utils.aoa_to_sheet(mainData);
    XLSX.utils.book_append_sheet(workbook, mainWorksheet, 'Dependency Batches');

    // 准备依赖关系详细数据
    const detailData = [['Package Name', 'Dependencies', 'Dependency Count']];
    batches.forEach(item => {
        const deps = dependencies.get(item.package) || [];
        detailData.push([
            item.package,
            deps.join(', '),
            deps.length
        ]);
    });

    // 创建详细信息工作表
    const detailWorksheet = XLSX.utils.aoa_to_sheet(detailData);
    XLSX.utils.book_append_sheet(workbook, detailWorksheet, 'Dependency Details');

    // 如果有错误，创建错误信息工作表
    if (errors.length > 0) {
        const errorData = [['Error Messages']];
        errors.forEach(error => {
            errorData.push([error]);
        });
        const errorWorksheet = XLSX.utils.aoa_to_sheet(errorData);
        XLSX.utils.book_append_sheet(workbook, errorWorksheet, 'Errors');
    }

    // 保存文件
    XLSX.writeFile(workbook, 'dependency-batches.xlsx');

    return 'dependency-batches.xlsx';
}

// 主函数
async function main() {
    console.log('Starting dependency analysis...');
    
    // 读取包列表
    const packages = readPackageList();
    console.log(`Found ${packages.length} packages to analyze`);
    
    // 获取所有包的依赖信息
    const dependencies = new Map();
    const errors = [];
    
    console.log('Fetching dependency information...');
    for (let i = 0; i < packages.length; i++) {
        const pkg = packages[i];
        console.log(`Processing ${i + 1}/${packages.length}: ${pkg}`);
        
        try {
            const deps = await getPackageDependencies(pkg);
            dependencies.set(pkg, deps);
            console.log(`  Found ${deps.length} @mu dependencies: ${deps.join(', ')}`);
        } catch (error) {
            const errorMsg = `Failed to get dependencies for ${pkg}: ${error.message}`;
            console.error(`  Error: ${errorMsg}`);
            errors.push(errorMsg);
            dependencies.set(pkg, []);
        }
    }
    
    console.log('\nCalculating modification batches...');
    
    // 执行拓扑排序
    const batches = topologicalSort(packages, dependencies);

    // 生成Excel文件
    const outputFile = generateExcel(batches, errors, dependencies);

    console.log('\nResults:');
    console.log(`Total packages: ${packages.length}`);
    console.log(`Total batches: ${Math.max(...batches.map(b => b.batch))}`);
    console.log(`Packages with errors: ${errors.length}`);

    // 显示批次统计
    const batchStats = {};
    batches.forEach(item => {
        if (!batchStats[item.batch]) {
            batchStats[item.batch] = 0;
        }
        batchStats[item.batch]++;
    });

    console.log('\nBatch statistics:');
    Object.keys(batchStats).sort((a, b) => parseInt(a) - parseInt(b)).forEach(batch => {
        console.log(`  Batch ${batch}: ${batchStats[batch]} packages`);
    });

    console.log(`\nOutput saved to: ${outputFile}`);
    
    if (errors.length > 0) {
        console.log('\nWarnings:');
        errors.forEach(error => console.log(`  - ${error}`));
    }
}

// 运行主函数
main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
});
