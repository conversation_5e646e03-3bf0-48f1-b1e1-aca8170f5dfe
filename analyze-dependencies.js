#!/usr/bin/env node

const XLSX = require('xlsx');

// 从 list.xlsx 文件读取包列表和依赖关系
// 从 Excel 文件读取包列表和依赖关系
function readPackageDataFromExcel() {
    try {
        const workbook = XLSX.readFile('list.xlsx');
        const worksheet = workbook.Sheets['Dependency Details'];
        const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        const packages = [];
        const dependencies = new Map();

        // 跳过标题行，从第二行开始
        for (let i = 1; i < data.length; i++) {
            const [packageName, dependenciesStr] = data[i];

            if (packageName && packageName.startsWith('@mu/')) {
                packages.push(packageName);

                // 解析依赖关系
                const deps = [];
                if (dependenciesStr && dependenciesStr.trim()) {
                    // 按逗号分割依赖，并清理空格
                    const depList = dependenciesStr.split(',').map(dep => dep.trim());
                    depList.forEach(dep => {
                        if (dep && dep.startsWith('@mu/')) {
                            deps.push(dep);
                        }
                    });
                }

                dependencies.set(packageName, deps);
            }
        }

        console.log(`从 Excel 文件读取到 ${packages.length} 个包`);
        return { packages, dependencies };
    } catch (error) {
        console.error('读取 list.xlsx 文件失败:', error.message);
        throw error;
    }
}

// 这个函数现在不再使用，数据直接从 Excel 文件读取
// function readPackageList() { ... }

// 这个函数现在不再使用，依赖关系直接从 Excel 文件读取
// async function getPackageDependencies(packageName) { ... }

// 拓扑排序算法 - 处理循环依赖的版本
function topologicalSort(packages, dependencies) {
    console.log('\n=== 开始拓扑排序 ===');

    // 首先处理循环依赖
    const cycles = findCycles(packages, dependencies);
    console.log(`发现 ${cycles.length} 个循环依赖组`);

    // 创建循环依赖组的映射
    const cycleMap = new Map();
    const cycleGroups = [];

    cycles.forEach((cycle, index) => {
        const groupId = `cycle_${index}`;
        cycleGroups.push({ id: groupId, packages: cycle });
        cycle.forEach(pkg => {
            cycleMap.set(pkg, groupId);
        });
        console.log(`循环依赖组 ${index + 1}: [${cycle.join(', ')}]`);
    });

    // 创建新的包列表，将循环依赖组作为单个节点
    const nodes = [];
    const nodeToPackages = new Map();
    const packageToNode = new Map();

    // 添加非循环包
    packages.forEach(pkg => {
        if (!cycleMap.has(pkg)) {
            nodes.push(pkg);
            nodeToPackages.set(pkg, [pkg]);
            packageToNode.set(pkg, pkg);
        }
    });

    // 添加循环依赖组
    cycleGroups.forEach(group => {
        nodes.push(group.id);
        nodeToPackages.set(group.id, group.packages);
        group.packages.forEach(pkg => {
            packageToNode.set(pkg, group.id);
        });
    });

    // 构建节点间的依赖关系
    const nodeDependencies = new Map();
    nodes.forEach(node => {
        nodeDependencies.set(node, new Set());
    });

    packages.forEach(pkg => {
        const pkgNode = packageToNode.get(pkg);
        const deps = dependencies.get(pkg) || [];

        deps.forEach(dep => {
            if (packages.includes(dep)) {
                const depNode = packageToNode.get(dep);
                if (pkgNode !== depNode) { // 不同节点间的依赖
                    nodeDependencies.get(pkgNode).add(depNode);
                }
            }
        });
    });

    // 对节点进行拓扑排序
    const graph = new Map();
    const inDegree = new Map();

    nodes.forEach(node => {
        graph.set(node, []);
        inDegree.set(node, 0);
    });

    // 构建图
    nodes.forEach(node => {
        const deps = Array.from(nodeDependencies.get(node));
        deps.forEach(dep => {
            graph.get(dep).push(node);
            inDegree.set(node, inDegree.get(node) + 1);
        });
    });

    // 执行拓扑排序
    const result = [];
    const batches = [];
    let batchNumber = 1;

    while (result.length < nodes.length) {
        const currentBatch = [];
        nodes.forEach(node => {
            if (!result.includes(node) && inDegree.get(node) === 0) {
                currentBatch.push(node);
            }
        });

        if (currentBatch.length === 0) {
            console.error('❌ 仍然存在循环依赖');
            break;
        }

        console.log(`\n批次 ${batchNumber}:`);
        currentBatch.sort();

        currentBatch.forEach(node => {
            result.push(node);
            const nodePackages = nodeToPackages.get(node);

            console.log(`  节点: ${node} (包含 ${nodePackages.length} 个包)`);
            nodePackages.sort().forEach(pkg => {
                batches.push({ package: pkg, batch: batchNumber });
                console.log(`    - ${pkg}`);
            });

            // 更新入度
            const dependents = graph.get(node) || [];
            dependents.forEach(dependent => {
                inDegree.set(dependent, inDegree.get(dependent) - 1);
            });
        });

        batchNumber++;
    }

    return batches;
}

// 查找循环依赖
function findCycles(packages, dependencies) {
    const graph = new Map();
    const reverseGraph = new Map();

    // 初始化图
    packages.forEach(pkg => {
        graph.set(pkg, []);
        reverseGraph.set(pkg, []);
    });

    // 构建图和反向图
    packages.forEach(pkg => {
        const deps = dependencies.get(pkg) || [];
        deps.forEach(dep => {
            if (packages.includes(dep)) {
                graph.get(pkg).push(dep);
                reverseGraph.get(dep).push(pkg);
            }
        });
    });

    // Kosaraju算法查找强连通分量
    const visited = new Set();
    const stack = [];

    function dfs1(node) {
        visited.add(node);
        const neighbors = graph.get(node) || [];
        neighbors.forEach(neighbor => {
            if (!visited.has(neighbor)) {
                dfs1(neighbor);
            }
        });
        stack.push(node);
    }

    packages.forEach(pkg => {
        if (!visited.has(pkg)) {
            dfs1(pkg);
        }
    });

    visited.clear();
    const components = [];

    function dfs2(node, component) {
        visited.add(node);
        component.push(node);
        const neighbors = reverseGraph.get(node) || [];
        neighbors.forEach(neighbor => {
            if (!visited.has(neighbor)) {
                dfs2(neighbor, component);
            }
        });
    }

    while (stack.length > 0) {
        const node = stack.pop();
        if (!visited.has(node)) {
            const component = [];
            dfs2(node, component);
            if (component.length > 1) {
                components.push(component);
            }
        }
    }

    return components;
}

// 生成Excel文件
function generateExcel(batches, errors, dependencies) {
    // 创建工作簿
    const workbook = XLSX.utils.book_new();

    // 准备主要数据
    const mainData = [['Package Name', 'Batch Number']];
    batches.forEach(item => {
        mainData.push([item.package, item.batch]);
    });

    // 创建主工作表
    const mainWorksheet = XLSX.utils.aoa_to_sheet(mainData);
    XLSX.utils.book_append_sheet(workbook, mainWorksheet, 'Dependency Batches');

    // 准备依赖关系详细数据
    const detailData = [['Package Name', 'Dependencies', 'Dependency Count']];
    batches.forEach(item => {
        const deps = dependencies.get(item.package) || [];
        detailData.push([
            item.package,
            deps.join(', '),
            deps.length
        ]);
    });

    // 创建详细信息工作表
    const detailWorksheet = XLSX.utils.aoa_to_sheet(detailData);
    XLSX.utils.book_append_sheet(workbook, detailWorksheet, 'Dependency Details');

    // 如果有错误，创建错误信息工作表
    if (errors.length > 0) {
        const errorData = [['Error Messages']];
        errors.forEach(error => {
            errorData.push([error]);
        });
        const errorWorksheet = XLSX.utils.aoa_to_sheet(errorData);
        XLSX.utils.book_append_sheet(workbook, errorWorksheet, 'Errors');
    }

    // 保存文件
    XLSX.writeFile(workbook, 'dependency-batches.xlsx');

    return 'dependency-batches.xlsx';
}

// 主函数
async function main() {
    console.log('Starting dependency analysis...');
    console.log('Data source: list.xlsx file');

    // 从 Excel 文件读取包列表和依赖关系
    const { packages, dependencies } = readPackageDataFromExcel();
    console.log(`Found ${packages.length} packages to analyze`);

    const errors = [];
    
    console.log('\nCalculating modification batches...');
    
    // 执行拓扑排序
    const batches = topologicalSort(packages, dependencies);

    // 生成Excel文件
    const outputFile = generateExcel(batches, errors, dependencies);

    console.log('\nResults:');
    console.log(`Total packages: ${packages.length}`);
    console.log(`Total batches: ${Math.max(...batches.map(b => b.batch))}`);
    console.log(`Packages with errors: ${errors.length}`);

    // 显示批次统计
    const batchStats = {};
    batches.forEach(item => {
        if (!batchStats[item.batch]) {
            batchStats[item.batch] = 0;
        }
        batchStats[item.batch]++;
    });

    console.log('\nBatch statistics:');
    Object.keys(batchStats).sort((a, b) => parseInt(a) - parseInt(b)).forEach(batch => {
        console.log(`  Batch ${batch}: ${batchStats[batch]} packages`);
    });

    console.log(`\nOutput saved to: ${outputFile}`);
    
    if (errors.length > 0) {
        console.log('\nWarnings:');
        errors.forEach(error => console.log(`  - ${error}`));
    }
}

// 运行主函数
main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
});
