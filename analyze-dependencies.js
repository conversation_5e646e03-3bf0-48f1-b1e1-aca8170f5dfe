#!/usr/bin/env node

const fs = require('fs');
const { execSync } = require('child_process');
const path = require('path');

// 读取包列表
function readPackageList() {
    try {
        const content = fs.readFileSync('list.md', 'utf8');
        return content.split('\n')
            .map(line => line.trim())
            .filter(line => line && line.startsWith('@mu/'));
    } catch (error) {
        console.error('Error reading list.md:', error.message);
        process.exit(1);
    }
}

// 获取包的依赖信息
async function getPackageDependencies(packageName) {
    const dependencies = new Set();
    
    try {
        // 获取生产依赖
        try {
            const depsOutput = execSync(`npm view ${packageName} dependencies --json`, { 
                encoding: 'utf8',
                timeout: 30000 
            });
            if (depsOutput.trim()) {
                const deps = JSON.parse(depsOutput);
                if (deps && typeof deps === 'object') {
                    Object.keys(deps).forEach(dep => {
                        if (dep.startsWith('@mu/')) {
                            dependencies.add(dep);
                        }
                    });
                }
            }
        } catch (error) {
            // 忽略生产依赖获取失败
        }

        // 获取开发依赖
        try {
            const devDepsOutput = execSync(`npm view ${packageName} devDependencies --json`, { 
                encoding: 'utf8',
                timeout: 30000 
            });
            if (devDepsOutput.trim()) {
                const devDeps = JSON.parse(devDepsOutput);
                if (devDeps && typeof devDeps === 'object') {
                    Object.keys(devDeps).forEach(dep => {
                        if (dep.startsWith('@mu/')) {
                            dependencies.add(dep);
                        }
                    });
                }
            }
        } catch (error) {
            // 忽略开发依赖获取失败
        }

        return Array.from(dependencies);
    } catch (error) {
        console.warn(`Warning: Failed to get dependencies for ${packageName}: ${error.message}`);
        return [];
    }
}

// 拓扑排序算法
function topologicalSort(packages, dependencies) {
    const graph = new Map();
    const inDegree = new Map();
    const result = [];
    const batches = [];
    
    // 初始化图和入度
    packages.forEach(pkg => {
        graph.set(pkg, []);
        inDegree.set(pkg, 0);
    });
    
    // 构建图
    packages.forEach(pkg => {
        const deps = dependencies.get(pkg) || [];
        deps.forEach(dep => {
            if (packages.includes(dep)) {
                graph.get(dep).push(pkg);
                inDegree.set(pkg, inDegree.get(pkg) + 1);
            }
        });
    });
    
    // 拓扑排序
    let batchNumber = 1;
    
    while (result.length < packages.length) {
        // 找到所有入度为0的节点
        const currentBatch = [];
        packages.forEach(pkg => {
            if (!result.includes(pkg) && inDegree.get(pkg) === 0) {
                currentBatch.push(pkg);
            }
        });
        
        if (currentBatch.length === 0) {
            // 检测到循环依赖
            const remaining = packages.filter(pkg => !result.includes(pkg));
            console.error('Circular dependency detected among packages:', remaining);
            
            // 将剩余包分配到当前批次
            remaining.forEach(pkg => {
                batches.push({ package: pkg, batch: batchNumber });
                result.push(pkg);
            });
            break;
        }
        
        // 将当前批次的包添加到结果中
        currentBatch.sort(); // 按字母顺序排序
        currentBatch.forEach(pkg => {
            batches.push({ package: pkg, batch: batchNumber });
            result.push(pkg);
            
            // 更新依赖包的入度
            graph.get(pkg).forEach(dependent => {
                inDegree.set(dependent, inDegree.get(dependent) - 1);
            });
        });
        
        batchNumber++;
    }
    
    return batches;
}

// 生成CSV文件
function generateCSV(batches, errors) {
    const csvContent = ['Package Name,Batch Number'];
    
    batches.forEach(item => {
        csvContent.push(`${item.package},${item.batch}`);
    });
    
    // 添加错误信息作为注释
    if (errors.length > 0) {
        csvContent.push('');
        csvContent.push('# Packages with errors:');
        errors.forEach(error => {
            csvContent.push(`# ${error}`);
        });
    }
    
    return csvContent.join('\n');
}

// 主函数
async function main() {
    console.log('Starting dependency analysis...');
    
    // 读取包列表
    const packages = readPackageList();
    console.log(`Found ${packages.length} packages to analyze`);
    
    // 获取所有包的依赖信息
    const dependencies = new Map();
    const errors = [];
    
    console.log('Fetching dependency information...');
    for (let i = 0; i < packages.length; i++) {
        const pkg = packages[i];
        console.log(`Processing ${i + 1}/${packages.length}: ${pkg}`);
        
        try {
            const deps = await getPackageDependencies(pkg);
            dependencies.set(pkg, deps);
            console.log(`  Found ${deps.length} @mu dependencies: ${deps.join(', ')}`);
        } catch (error) {
            const errorMsg = `Failed to get dependencies for ${pkg}: ${error.message}`;
            console.error(`  Error: ${errorMsg}`);
            errors.push(errorMsg);
            dependencies.set(pkg, []);
        }
    }
    
    console.log('\nCalculating modification batches...');
    
    // 执行拓扑排序
    const batches = topologicalSort(packages, dependencies);
    
    // 生成CSV文件
    const csvContent = generateCSV(batches, errors);
    fs.writeFileSync('dependency-batches.csv', csvContent);
    
    console.log('\nResults:');
    console.log(`Total packages: ${packages.length}`);
    console.log(`Total batches: ${Math.max(...batches.map(b => b.batch))}`);
    console.log(`Packages with errors: ${errors.length}`);
    
    // 显示批次统计
    const batchStats = {};
    batches.forEach(item => {
        if (!batchStats[item.batch]) {
            batchStats[item.batch] = 0;
        }
        batchStats[item.batch]++;
    });
    
    console.log('\nBatch statistics:');
    Object.keys(batchStats).sort((a, b) => parseInt(a) - parseInt(b)).forEach(batch => {
        console.log(`  Batch ${batch}: ${batchStats[batch]} packages`);
    });
    
    console.log('\nOutput saved to: dependency-batches.csv');
    
    if (errors.length > 0) {
        console.log('\nWarnings:');
        errors.forEach(error => console.log(`  - ${error}`));
    }
}

// 运行主函数
main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
});
