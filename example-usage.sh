#!/bin/bash

echo "=== NPM 包依赖关系分析工具使用示例 ==="
echo

echo "1. 查看当前数据源配置"
node switch-data-source.js
echo

echo "2. 确保使用 Mock 数据（推荐用于演示）"
node switch-data-source.js mock
echo

echo "3. 运行依赖分析"
echo "正在分析 134 个 @mu 包的依赖关系..."
node analyze-dependencies.js
echo

echo "4. 查看分析结果"
node view-results.js
echo

echo "=== 分析完成 ==="
echo "生成的文件："
echo "  - dependency-batches.xlsx (Excel 报告文件)"
echo "  - README.md (详细使用说明)"
echo
echo "如需使用真实 npm 数据，请运行："
echo "  node switch-data-source.js real"
echo "  node analyze-dependencies.js"
echo
echo "注意：使用真实数据需要网络连接，且执行时间较长"
