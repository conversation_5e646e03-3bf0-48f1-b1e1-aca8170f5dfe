#!/usr/bin/env node

const XLSX = require('xlsx');

// 从 Excel 文件读取包列表和依赖关系
function readPackageDataFromExcel() {
    try {
        const workbook = XLSX.readFile('list.xlsx');
        const worksheet = workbook.Sheets['Dependency Details'];
        const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        const packages = [];
        const dependencies = new Map();
        
        // 跳过标题行，从第二行开始
        for (let i = 1; i < data.length; i++) {
            const [packageName, dependenciesStr] = data[i];
            
            if (packageName && packageName.startsWith('@mu/')) {
                packages.push(packageName);
                
                // 解析依赖关系
                const deps = [];
                if (dependenciesStr && dependenciesStr.trim()) {
                    // 按逗号分割依赖，并清理空格
                    const depList = dependenciesStr.split(',').map(dep => dep.trim());
                    depList.forEach(dep => {
                        if (dep && dep.startsWith('@mu/')) {
                            deps.push(dep);
                        }
                    });
                }
                
                dependencies.set(packageName, deps);
            }
        }
        
        return { packages, dependencies };
    } catch (error) {
        console.error('读取 list.xlsx 文件失败:', error.message);
        throw error;
    }
}

// 检测强连通分量（循环依赖）
function findStronglyConnectedComponents(packages, dependencies) {
    const graph = new Map();
    const reverseGraph = new Map();
    
    // 初始化图
    packages.forEach(pkg => {
        graph.set(pkg, []);
        reverseGraph.set(pkg, []);
    });
    
    // 构建图和反向图
    packages.forEach(pkg => {
        const deps = dependencies.get(pkg) || [];
        deps.forEach(dep => {
            if (packages.includes(dep)) {
                graph.get(pkg).push(dep);
                reverseGraph.get(dep).push(pkg);
            }
        });
    });
    
    // Kosaraju算法
    const visited = new Set();
    const stack = [];
    
    // 第一次DFS，按完成时间排序
    function dfs1(node) {
        visited.add(node);
        const neighbors = graph.get(node) || [];
        neighbors.forEach(neighbor => {
            if (!visited.has(neighbor)) {
                dfs1(neighbor);
            }
        });
        stack.push(node);
    }
    
    packages.forEach(pkg => {
        if (!visited.has(pkg)) {
            dfs1(pkg);
        }
    });
    
    // 第二次DFS，在反向图上
    visited.clear();
    const components = [];
    
    function dfs2(node, component) {
        visited.add(node);
        component.push(node);
        const neighbors = reverseGraph.get(node) || [];
        neighbors.forEach(neighbor => {
            if (!visited.has(neighbor)) {
                dfs2(neighbor, component);
            }
        });
    }
    
    while (stack.length > 0) {
        const node = stack.pop();
        if (!visited.has(node)) {
            const component = [];
            dfs2(node, component);
            components.push(component);
        }
    }
    
    return components;
}

// 分析循环依赖
function analyzeCycles() {
    console.log('=== 循环依赖分析 ===\n');
    
    const { packages, dependencies } = readPackageDataFromExcel();
    console.log(`总包数: ${packages.length}\n`);
    
    // 查找强连通分量
    const components = findStronglyConnectedComponents(packages, dependencies);
    
    // 过滤出包含多个包的强连通分量（循环依赖）
    const cycles = components.filter(component => component.length > 1);
    
    console.log(`发现 ${cycles.length} 个循环依赖组:\n`);
    
    cycles.forEach((cycle, index) => {
        console.log(`循环依赖组 ${index + 1} (${cycle.length} 个包):`);
        cycle.sort().forEach(pkg => {
            const deps = dependencies.get(pkg) || [];
            const cycleDeps = deps.filter(dep => cycle.includes(dep));
            console.log(`  ${pkg} -> [${cycleDeps.join(', ')}]`);
        });
        console.log();
    });
    
    // 分析特定包的依赖路径
    console.log('=== 特定包分析 ===\n');
    
    const targetPairs = [
        ['@mu/op-ui', '@mu/cmb-unite-apply'],
        ['@mu/cmb-unite-apply', '@mu/op-ui']
    ];
    
    targetPairs.forEach(([from, to]) => {
        console.log(`查找从 ${from} 到 ${to} 的依赖路径:`);
        const path = findDependencyPath(from, to, dependencies, packages);
        if (path) {
            console.log(`  路径: ${path.join(' -> ')}`);
        } else {
            console.log(`  未找到直接依赖路径`);
        }
        console.log();
    });
}

// 查找依赖路径
function findDependencyPath(from, to, dependencies, packages) {
    const visited = new Set();
    const queue = [[from]];
    
    while (queue.length > 0) {
        const path = queue.shift();
        const current = path[path.length - 1];
        
        if (current === to && path.length > 1) {
            return path;
        }
        
        if (visited.has(current)) {
            continue;
        }
        visited.add(current);
        
        const deps = dependencies.get(current) || [];
        deps.forEach(dep => {
            if (packages.includes(dep) && !visited.has(dep)) {
                queue.push([...path, dep]);
            }
        });
    }
    
    return null;
}

analyzeCycles();
