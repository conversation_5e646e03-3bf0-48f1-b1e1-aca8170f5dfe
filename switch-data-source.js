#!/usr/bin/env node

const fs = require('fs');

function switchDataSource(useMock) {
    try {
        // 读取当前的分析脚本
        let content = fs.readFileSync('analyze-dependencies.js', 'utf8');
        
        // 查找并替换配置行
        const configRegex = /const USE_MOCK_DATA = (true|false);/;
        const newValue = useMock ? 'true' : 'false';
        const newLine = `const USE_MOCK_DATA = ${newValue};`;
        
        if (configRegex.test(content)) {
            content = content.replace(configRegex, newLine);
            fs.writeFileSync('analyze-dependencies.js', content);
            
            console.log(`✅ Data source switched to: ${useMock ? 'Mock data' : 'Real npm view data'}`);
            
            if (!useMock) {
                console.log('⚠️  Note: Make sure you have network access to npm registry');
                console.log('⚠️  Real npm view commands may take longer to execute');
            }
        } else {
            console.error('❌ Could not find USE_MOCK_DATA configuration in analyze-dependencies.js');
        }
    } catch (error) {
        console.error('❌ Error switching data source:', error.message);
    }
}

function showUsage() {
    console.log('Usage: node switch-data-source.js [mock|real]');
    console.log('');
    console.log('Options:');
    console.log('  mock  - Use mock data (faster, for testing)');
    console.log('  real  - Use real npm view data (requires network)');
    console.log('');
    console.log('Examples:');
    console.log('  node switch-data-source.js mock');
    console.log('  node switch-data-source.js real');
}

function getCurrentDataSource() {
    try {
        const content = fs.readFileSync('analyze-dependencies.js', 'utf8');
        const match = content.match(/const USE_MOCK_DATA = (true|false);/);
        
        if (match) {
            const useMock = match[1] === 'true';
            console.log(`Current data source: ${useMock ? 'Mock data' : 'Real npm view data'}`);
            return useMock;
        } else {
            console.log('❌ Could not determine current data source');
            return null;
        }
    } catch (error) {
        console.error('❌ Error reading current configuration:', error.message);
        return null;
    }
}

// 主逻辑
const args = process.argv.slice(2);

if (args.length === 0) {
    getCurrentDataSource();
    console.log('');
    showUsage();
} else if (args.length === 1) {
    const command = args[0].toLowerCase();
    
    switch (command) {
        case 'mock':
            switchDataSource(true);
            break;
        case 'real':
            switchDataSource(false);
            break;
        case 'status':
            getCurrentDataSource();
            break;
        case 'help':
        case '--help':
        case '-h':
            showUsage();
            break;
        default:
            console.error(`❌ Unknown command: ${command}`);
            console.log('');
            showUsage();
            process.exit(1);
    }
} else {
    console.error('❌ Too many arguments');
    console.log('');
    showUsage();
    process.exit(1);
}
