# NPM 包依赖关系分析工具

## 概述

这个工具用于分析一批 npm 包的依赖关系，并根据依赖关系计算出合理的修改批次顺序，确保在修改包时依赖包总是在被依赖包之前修改。

## 功能特性

- 📦 读取包列表文件 (list.md)
- 🔍 分析包之间的依赖关系（支持生产依赖和开发依赖）
- 🎯 过滤只保留 @mu 开头的内部依赖
- 📊 使用拓扑排序算法计算修改批次
- 📈 生成详细的 Excel 报告
- ⚠️ 检测循环依赖并报告异常情况

## 文件说明

- `list.md` - 包含需要分析的 npm 包名列表
- `analyze-dependencies.js` - 主要分析脚本
- `view-results.js` - 结果查看脚本
- `dependency-batches.xlsx` - 生成的 Excel 报告文件

## 使用方法

### 1. 安装依赖

```bash
npm install xlsx
```

### 2. 运行分析

```bash
node analyze-dependencies.js
```

### 3. 查看结果

```bash
node view-results.js
```

## 分析结果

### 批次统计

根据当前分析结果，134个包被分为6个修改批次：

- **批次 1**: 5个包 - 基础包，无依赖
- **批次 2**: 24个包 - 只依赖批次1的包
- **批次 3**: 60个包 - 依赖前两批次的包
- **批次 4**: 31个包 - 依赖前三批次的包
- **批次 5**: 11个包 - 依赖前四批次的包
- **批次 6**: 3个包 - 依赖前五批次的包

### 关键基础包（批次1）

这些包是整个依赖树的根基，应该最先修改：

- `@mu/babel-plugin-transform-madpapi`
- `@mu/basic-library`
- `@mu/local-config`
- `@mu/madp-base`
- `@mu/mini-html-parser2`

### 核心框架包（批次2）

这些包是核心框架组件，依赖基础包：

- `@mu/cui` - UI组件库
- `@mu/madp-utils` - MADP工具库
- `@mu/business-basic` - 业务基础库
- `@mu/login-utils` - 登录工具库
- 等等...

## Excel 报告内容

生成的 Excel 文件包含三个工作表：

1. **Dependency Batches** - 包名和对应的批次号
2. **Dependency Details** - 每个包的详细依赖信息
3. **Errors** - 分析过程中的错误信息（如果有）

## 修改建议

### 修改顺序

1. **第一批次**：修改无依赖的基础包
2. **第二批次**：修改只依赖第一批次的包
3. **第三批次**：修改只依赖前两批次的包
4. **以此类推**...

### 注意事项

- ⚠️ 严格按照批次顺序进行修改
- ⚠️ 同一批次内的包可以并行修改
- ⚠️ 修改前确认依赖关系没有变化
- ⚠️ 如果发现循环依赖，需要先解决循环依赖问题

## 技术实现

### 拓扑排序算法

使用 Kahn 算法实现拓扑排序：

1. 计算每个节点的入度
2. 将入度为0的节点加入队列
3. 处理队列中的节点，更新其邻接节点的入度
4. 重复直到所有节点都被处理

### 依赖关系获取

- 使用 `npm view` 命令获取包的依赖信息
- 支持 Mock 数据模式用于测试
- 过滤只保留 @mu 开头的内部依赖

### 循环依赖检测

如果在拓扑排序过程中发现无法继续处理的节点，说明存在循环依赖，工具会：

- 报告涉及循环依赖的包
- 将这些包分配到当前批次
- 在控制台输出警告信息

## 扩展功能

### 支持真实 npm 数据

当网络可用时，可以修改 `getPackageDependencies` 函数来获取真实的 npm 包信息：

```javascript
// 取消注释真实的 npm view 调用
const depsOutput = execSync(`npm view ${packageName} dependencies --json`);
```

### 自定义过滤规则

可以修改依赖过滤逻辑来支持其他包前缀或命名规则。

## 故障排除

### 常见问题

1. **网络问题**：使用 Mock 数据模式
2. **包不存在**：检查 list.md 中的包名是否正确
3. **循环依赖**：查看控制台输出的循环依赖报告

### 日志信息

工具会输出详细的处理日志，包括：
- 每个包的依赖获取进度
- 发现的依赖关系
- 批次计算结果
- 错误和警告信息
